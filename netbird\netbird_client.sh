#!/bin/bash

# Netbird client installation, removal, and gateway configuration functions

# Function to validate setup key format
validate_setup_key() {
    local setup_key="$1"
    
    # Basic validation - setup key should be a UUID-like format
    if [[ ! "$setup_key" =~ ^[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}$ ]]; then
        echo -e "${RED}Invalid setup key format. Expected format: XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX${NC}"
        return 1
    fi
    return 0
}

# Function to validate management URL format
validate_management_url() {
    local url="$1"
    
    # Basic URL validation
    if [[ ! "$url" =~ ^https?://[a-zA-Z0-9][a-zA-Z0-9.-]*[a-zA-Z0-9]\.[a-zA-Z]{2,}(:[0-9]+)?/?$ ]]; then
        echo -e "${RED}Invalid management URL format. Expected format: https://netbird.example.com${NC}"
        return 1
    fi
    return 0
}

# Function to detect primary network interface
detect_primary_interface() {
    local interface=""
    
    # Method 1: Get interface with default route
    interface=$(ip route | grep '^default' | head -1 | awk '{print $5}')
    
    if [ -n "$interface" ] && [ "$interface" != "lo" ]; then
        echo "$interface"
        return 0
    fi
    
    # Method 2: Get first non-loopback interface with an IP
    interface=$(ip addr show | grep -E '^[0-9]+:' | grep -v 'lo:' | head -1 | awk -F': ' '{print $2}')
    
    if [ -n "$interface" ]; then
        echo "$interface"
        return 0
    fi
    
    # Fallback
    echo "eth0"
    return 1
}

# Function to check if Netbird client is installed
is_netbird_client_installed() {
    if command -v netbird >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to check if Netbird client is running
is_netbird_client_running() {
    if systemctl is-active --quiet netbird; then
        return 0
    else
        return 1
    fi
}

# Function to install Netbird client
install_netbird_client() {
    echo "Installing Netbird Client..."
    echo "============================"
    
    # Check if running on Ubuntu/Debian
    if ! command -v apt-get >/dev/null 2>&1; then
        echo -e "${RED}This installation method is designed for Ubuntu/Debian systems.${NC}"
        echo "For other distributions, please refer to the Netbird documentation."
        return 1
    fi
    
    # Check if already installed
    if is_netbird_client_installed; then
        echo -e "${YELLOW}Netbird client is already installed.${NC}"
        read -rp "Would you like to reinstall? (y/n): " reinstall_choice
        if [[ ! "$reinstall_choice" =~ ^[Yy]$ ]]; then
            echo "Installation cancelled."
            return 1
        fi
        
        # Remove existing installation
        echo "Removing existing installation..."
        if ! remove_netbird_client; then
            echo -e "${RED}Failed to remove existing installation.${NC}"
            return 1
        fi
    fi
    
    # Check prerequisites
    echo "Checking prerequisites..."
    
    # Check if running as root or with sudo access
    if [ "$EUID" -ne 0 ] && ! sudo -n true 2>/dev/null; then
        echo -e "${RED}This script requires root privileges or sudo access.${NC}"
        return 1
    fi
    
    # Install required tools
    echo "Installing required tools..."
    if ! command -v curl >/dev/null 2>&1; then
        echo "Installing curl..."
        sudo apt-get update && sudo apt-get install -y curl
    fi
    
    if ! command -v gpg >/dev/null 2>&1; then
        echo "Installing gnupg..."
        sudo apt-get update && sudo apt-get install -y gnupg
    fi
    
    # Add Netbird repository
    echo "Adding Netbird repository..."
    if ! curl -fsSL https://pkgs.netbird.io/debian/public.key | sudo gpg --dearmor -o /usr/share/keyrings/netbird-archive-keyring.gpg; then
        echo -e "${RED}Failed to add Netbird GPG key.${NC}"
        return 1
    fi
    
    if ! echo "deb [signed-by=/usr/share/keyrings/netbird-archive-keyring.gpg] https://pkgs.netbird.io/debian stable main" | sudo tee /etc/apt/sources.list.d/netbird.list; then
        echo -e "${RED}Failed to add Netbird repository.${NC}"
        return 1
    fi
    
    # Update package list
    echo "Updating package list..."
    if ! sudo apt-get update; then
        echo -e "${RED}Failed to update package list.${NC}"
        return 1
    fi
    
    # Install Netbird client
    echo "Installing Netbird client..."
    if ! sudo apt-get install netbird -y; then
        echo -e "${RED}Failed to install Netbird client.${NC}"
        return 1
    fi
    
    echo -e "${GREEN}Netbird client installed successfully!${NC}"
    
    # Get setup key and management URL
    local setup_key=""
    local management_url=""
    
    echo ""
    echo "Netbird Client Configuration"
    echo "============================"
    
    # Get setup key
    while [ -z "$setup_key" ]; do
        read -rp "Enter setup key (e.g., 3216EC5D-F818-4484-B7A1-D6FCF79C8443): " setup_key
        
        if [ -z "$setup_key" ]; then
            echo -e "${RED}Setup key is required.${NC}"
            continue
        fi
        
        if ! validate_setup_key "$setup_key"; then
            setup_key=""
            continue
        fi
        
        break
    done
    
    # Get management URL
    while [ -z "$management_url" ]; do
        read -rp "Enter management URL (e.g., https://netbird.fazee.pw): " management_url
        
        if [ -z "$management_url" ]; then
            echo -e "${RED}Management URL is required.${NC}"
            continue
        fi
        
        if ! validate_management_url "$management_url"; then
            management_url=""
            continue
        fi
        
        break
    done
    
    # Connect to Netbird
    echo ""
    echo "Connecting to Netbird..."
    if netbird up --setup-key "$setup_key" --management-url "$management_url"; then
        echo -e "${GREEN}Successfully connected to Netbird!${NC}"
    else
        echo -e "${RED}Failed to connect to Netbird.${NC}"
        echo "You can try connecting manually with:"
        echo "netbird up --setup-key $setup_key --management-url $management_url"
        return 1
    fi
    
    # Ask about gateway configuration
    echo ""
    read -rp "Would you like to configure this server as a gateway for other networks? (y/n): " gateway_choice
    if [[ "$gateway_choice" =~ ^[Yy]$ ]]; then
        setup_netbird_gateway
    fi
    
    echo ""
    echo -e "${GREEN}Netbird client installation and configuration completed!${NC}"
    echo ""
    echo "You can check the status with: netbird status"
    echo "You can view logs with: journalctl -u netbird -f"
    
    return 0
}

# Function to setup Netbird gateway
setup_netbird_gateway() {
    echo ""
    echo "Setting up Netbird Gateway"
    echo "=========================="
    
    # Enable IP forwarding
    echo "Enabling IP forwarding..."
    if ! echo "net.ipv4.ip_forward=1" | sudo tee /etc/sysctl.d/99-netbird.conf; then
        echo -e "${RED}Failed to enable IP forwarding.${NC}"
        return 1
    fi
    
    if ! sudo sysctl -p /etc/sysctl.d/99-netbird.conf; then
        echo -e "${RED}Failed to apply IP forwarding settings.${NC}"
        return 1
    fi
    
    echo -e "${GREEN}IP forwarding enabled.${NC}"
    
    # Get networks to route
    local networks=""
    echo ""
    echo "Enter the network(s) you want to route through this gateway."
    echo "Examples: **********/24, ***********/24, 10.0.0.0/8"
    echo "You can enter multiple networks separated by commas."
    
    while [ -z "$networks" ]; do
        read -rp "Enter network(s): " networks
        
        if [ -z "$networks" ]; then
            echo -e "${RED}At least one network is required.${NC}"
            continue
        fi
        
        break
    done
    
    # Detect primary interface
    local primary_interface=$(detect_primary_interface)
    echo ""
    echo "Auto-detected primary interface: $primary_interface"
    read -rp "Press Enter to use this interface, or type a different one: " custom_interface
    
    if [ -n "$custom_interface" ]; then
        primary_interface="$custom_interface"
    fi
    
    echo "Using interface: $primary_interface"
    
    # Install iptables-persistent
    echo ""
    echo "Installing iptables-persistent..."
    export DEBIAN_FRONTEND=noninteractive
    if ! sudo apt-get install -y iptables-persistent; then
        echo -e "${YELLOW}Warning: Failed to install iptables-persistent. Rules may not persist after reboot.${NC}"
    fi
    
    # Setup iptables rules
    echo "Setting up iptables rules..."
    
    # Convert comma-separated networks to array
    IFS=',' read -ra network_array <<< "$networks"
    
    for network in "${network_array[@]}"; do
        # Trim whitespace
        network=$(echo "$network" | xargs)
        
        echo "Setting up rules for network: $network"
        
        # Add MASQUERADE rule for the network
        sudo iptables -t nat -A POSTROUTING -s "$network" -o "$primary_interface" -j MASQUERADE
        
        # Add FORWARD rules
        sudo iptables -A FORWARD -i netbird0 -o "$primary_interface" -j ACCEPT
        sudo iptables -A FORWARD -i "$primary_interface" -o netbird0 -m state --state RELATED,ESTABLISHED -j ACCEPT
    done
    
    # Save iptables rules
    echo "Saving iptables rules..."
    if command -v iptables-save >/dev/null 2>&1; then
        sudo iptables-save | sudo tee /etc/iptables/rules.v4 >/dev/null
        echo -e "${GREEN}Iptables rules saved.${NC}"
    else
        echo -e "${YELLOW}Warning: Could not save iptables rules. They may not persist after reboot.${NC}"
    fi
    
    echo ""
    echo -e "${GREEN}Gateway configuration completed!${NC}"
    echo ""
    echo "Configured networks: $networks"
    echo "Primary interface: $primary_interface"
    echo ""
    echo "Note: You may need to configure these routes in your Netbird management interface"
    echo "to advertise them to other peers in the network."
    
    return 0
}

# Function to remove Netbird client completely
remove_netbird_client() {
    echo "Removing Netbird Client..."
    echo "=========================="

    # Check if Netbird client is installed
    if ! is_netbird_client_installed; then
        echo -e "${YELLOW}Netbird client is not installed.${NC}"
        return 0
    fi

    # Confirm removal
    echo -e "${RED}WARNING: This will completely remove Netbird client and all its configuration!${NC}"
    read -rp "Are you sure you want to continue? (y/n): " confirm_removal
    if [[ ! "$confirm_removal" =~ ^[Yy]$ ]]; then
        echo "Removal cancelled."
        return 1
    fi

    # Stop Netbird service if running
    echo "Stopping Netbird service..."
    if is_netbird_client_running; then
        if ! sudo systemctl stop netbird; then
            echo -e "${YELLOW}Warning: Failed to stop Netbird service.${NC}"
        fi
    fi

    # Disconnect from Netbird
    echo "Disconnecting from Netbird..."
    if command -v netbird >/dev/null 2>&1; then
        netbird down 2>/dev/null || true
    fi

    # Remove Netbird package
    echo "Removing Netbird package..."
    if ! sudo apt-get remove --purge netbird -y; then
        echo -e "${YELLOW}Warning: Failed to remove Netbird package.${NC}"
    fi

    # Remove Netbird repository
    echo "Removing Netbird repository..."
    sudo rm -f /etc/apt/sources.list.d/netbird.list
    sudo rm -f /usr/share/keyrings/netbird-archive-keyring.gpg

    # Remove configuration files
    echo "Removing configuration files..."
    sudo rm -rf /etc/netbird
    sudo rm -rf ~/.netbird
    sudo rm -rf /var/lib/netbird

    # Remove systemd service files
    echo "Removing systemd service files..."
    sudo rm -f /etc/systemd/system/netbird.service
    sudo rm -f /lib/systemd/system/netbird.service
    sudo systemctl daemon-reload

    # Clean up IP forwarding settings
    echo "Cleaning up IP forwarding settings..."
    if [ -f /etc/sysctl.d/99-netbird.conf ]; then
        sudo rm -f /etc/sysctl.d/99-netbird.conf
        echo "Removed IP forwarding configuration."
    fi

    # Clean up iptables rules
    echo "Cleaning up iptables rules..."

    # Remove MASQUERADE rules (try common patterns)
    sudo iptables -t nat -D POSTROUTING -o $(detect_primary_interface) -j MASQUERADE 2>/dev/null || true

    # Remove FORWARD rules for netbird interface
    sudo iptables -D FORWARD -i netbird0 -j ACCEPT 2>/dev/null || true
    sudo iptables -D FORWARD -o netbird0 -j ACCEPT 2>/dev/null || true
    sudo iptables -D FORWARD -i netbird0 -o $(detect_primary_interface) -j ACCEPT 2>/dev/null || true
    sudo iptables -D FORWARD -i $(detect_primary_interface) -o netbird0 -m state --state RELATED,ESTABLISHED -j ACCEPT 2>/dev/null || true

    # Save cleaned iptables rules
    if command -v iptables-save >/dev/null 2>&1; then
        sudo iptables-save | sudo tee /etc/iptables/rules.v4 >/dev/null 2>&1 || true
    fi

    # Remove any remaining netbird interfaces
    echo "Removing network interfaces..."
    for interface in $(ip link show | grep netbird | awk -F': ' '{print $2}'); do
        sudo ip link delete "$interface" 2>/dev/null || true
    done

    # Update package list
    echo "Updating package list..."
    sudo apt-get update >/dev/null 2>&1 || true

    echo ""
    echo -e "${GREEN}Netbird client has been completely removed!${NC}"
    echo ""
    echo "Removed components:"
    echo "  ✓ Netbird package and binaries"
    echo "  ✓ Configuration files"
    echo "  ✓ Systemd service files"
    echo "  ✓ Repository configuration"
    echo "  ✓ IP forwarding settings"
    echo "  ✓ Iptables rules"
    echo "  ✓ Network interfaces"
    echo ""
    echo "Note: You may need to reboot to ensure all network changes take effect."

    return 0
}

# Function to check Netbird client status
check_netbird_client_status() {
    echo "Netbird Client Status"
    echo "===================="

    # Check if client is installed
    if ! is_netbird_client_installed; then
        echo -e "${RED}✗ Netbird client is not installed${NC}"
        echo ""
        echo "To install Netbird client, use the 'Install Netbird Client' option from the menu."
        return 1
    fi

    echo -e "${GREEN}✓ Netbird client is installed${NC}"

    # Check if service is running
    if is_netbird_client_running; then
        echo -e "${GREEN}✓ Netbird service is running${NC}"
    else
        echo -e "${RED}✗ Netbird service is not running${NC}"
    fi

    # Get client status
    echo ""
    echo "Client Information:"
    echo "==================="

    if command -v netbird >/dev/null 2>&1; then
        echo "Netbird version:"
        netbird version 2>/dev/null || echo "  Unable to get version"

        echo ""
        echo "Connection status:"
        netbird status 2>/dev/null || echo "  Unable to get status"

        echo ""
        echo "Peer information:"
        netbird list 2>/dev/null || echo "  Unable to get peer list"
    fi

    # Check network interface
    echo ""
    echo "Network Interface:"
    echo "=================="

    local netbird_interface=$(ip link show | grep netbird | awk -F': ' '{print $2}' | head -1)
    if [ -n "$netbird_interface" ]; then
        echo -e "${GREEN}✓ Netbird interface found: $netbird_interface${NC}"
        ip addr show "$netbird_interface" 2>/dev/null || echo "  Unable to show interface details"
    else
        echo -e "${RED}✗ No Netbird interface found${NC}"
    fi

    # Check IP forwarding if configured
    echo ""
    echo "Gateway Configuration:"
    echo "====================="

    if [ -f /etc/sysctl.d/99-netbird.conf ]; then
        echo -e "${GREEN}✓ IP forwarding configuration found${NC}"
        cat /etc/sysctl.d/99-netbird.conf

        echo ""
        echo "Current IP forwarding status:"
        sysctl net.ipv4.ip_forward
    else
        echo -e "${YELLOW}○ No gateway configuration found${NC}"
    fi

    # Check iptables rules
    echo ""
    echo "Iptables Rules:"
    echo "==============="

    local masquerade_rules=$(sudo iptables -t nat -L POSTROUTING -n | grep MASQUERADE | wc -l)
    local forward_rules=$(sudo iptables -L FORWARD -n | grep netbird | wc -l)

    if [ "$masquerade_rules" -gt 0 ] || [ "$forward_rules" -gt 0 ]; then
        echo -e "${GREEN}✓ Netbird-related iptables rules found${NC}"
        echo "MASQUERADE rules: $masquerade_rules"
        echo "FORWARD rules: $forward_rules"
    else
        echo -e "${YELLOW}○ No Netbird-related iptables rules found${NC}"
    fi

    echo ""
    return 0
}
