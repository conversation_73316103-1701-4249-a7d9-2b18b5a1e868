#!/bin/bash

# Main Netbird functions file - sources all modular components
# This file serves as the entry point that loads all specialized modules

# Get the directory where this script is located
NETBIRD_MODULE_DIR="$(dirname "${BASH_SOURCE[0]}")"

# Source all required modules
source "$NETBIRD_MODULE_DIR/netbird_core.sh"
source "$NETBIRD_MODULE_DIR/netbird_install.sh"
source "$NETBIRD_MODULE_DIR/netbird_client.sh"
source "$NETBIRD_MODULE_DIR/netbird_utils.sh"
source "$NETBIRD_MODULE_DIR/netbird_backup_config.sh"
source "$NETBIRD_MODULE_DIR/netbird_backup_data.sh"
source "$NETBIRD_MODULE_DIR/netbird_restore.sh"

# Module Overview:
# ================
# - netbird_core.sh: Core functions (detection, service management, status checking)
# - netbird_install.sh: Installation, removal, and update functions
# - netbird_client.sh: Client installation, removal, and gateway configuration
# - netbird_utils.sh: Utility functions (health check, debug, connectivity testing)
# - netbird_backup_config.sh: Configuration backup functions and utilities
# - netbird_backup_data.sh: Data and volume backup functions
# - netbird_restore.sh: All restore functionality (legacy and domain-based)
# - netbird_menu.sh: Menu interfaces and UI functions

# All functions are now available through their respective modules
# This modular approach keeps file sizes manageable (250-400 lines each)
# while maintaining all existing functionality
